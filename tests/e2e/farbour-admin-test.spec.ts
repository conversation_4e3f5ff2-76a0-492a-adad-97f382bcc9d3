import { test, expect } from '@playwright/test';
import {
  waitForPageLoad,
  signInAsAdmin,
  signInWithCredentials,
  signOut,
  verifyAdminAccess,
  navigateToAdminDashboard,
  testMainNavigation,
  verifyPageElements,
  takeScreenshot
} from './utils/test-helpers';

/**
 * Comprehensive test suite specifically for the farbour/admin user
 * Tests all functionality that should be available to this admin user
 */

test.describe('Farbour Admin User - Complete Functionality Test', () => {
  test.beforeEach(async ({ page }) => {
    // Start each test from a clean state
    await page.goto('/');
  });

  test('should successfully authenticate farbour user with admin credentials', async ({ page }) => {
    // Navigate to sign-in page
    await page.goto('/auth/signin');
    await waitForPageLoad(page);
    
    // Take screenshot of sign-in page
    await takeScreenshot(page, 'farbour-signin-page');
    
    // Sign in with farbour/admin credentials
    await signInAsAdmin(page);
    
    // Verify successful authentication
    expect(page.url()).toContain('/dashboard');
    
    // Verify dashboard is loaded
    const dashboardContent = page.locator('main, [role="main"], .main-content');
    await expect(dashboardContent).toBeVisible({ timeout: 10000 });
    
    // Take screenshot of successful login
    await takeScreenshot(page, 'farbour-successful-login');
  });

  test('should have admin access to all sections', async ({ page }) => {
    // Sign in as admin
    await signInAsAdmin(page);
    
    // Test access to all main sections
    const sections = [
      { path: '/dashboard', name: 'Dashboard' },
      { path: '/marketing', name: 'Marketing' },
      { path: '/admin', name: 'Admin Dashboard' }
    ];
    
    for (const section of sections) {
      await page.goto(section.path);
      await waitForPageLoad(page);
      
      // Verify we can access the section (not redirected to sign-in)
      expect(page.url()).toContain(section.path);
      expect(page.url()).not.toContain('/auth/signin');
      
      // Take screenshot
      await takeScreenshot(page, `farbour-access-${section.name.toLowerCase().replace(/\s+/g, '-')}`);
    }
  });

  test('should verify admin privileges and permissions', async ({ page }) => {
    // Sign in as admin
    await signInAsAdmin(page);
    
    // Verify admin access
    const hasAdminAccess = await verifyAdminAccess(page);
    expect(hasAdminAccess).toBe(true);
    
    // Navigate to admin dashboard
    await navigateToAdminDashboard(page);
    
    // Verify admin-specific elements are present
    await verifyPageElements(page, [
      'main, [role="main"], .main-content'
    ]);
    
    // Take screenshot of admin dashboard
    await takeScreenshot(page, 'farbour-admin-dashboard');
  });

  test('should maintain session across navigation', async ({ page }) => {
    // Sign in as admin
    await signInAsAdmin(page);
    
    // Navigate through different pages
    const navigationPaths = [
      '/dashboard',
      '/marketing',
      '/admin',
      '/dashboard' // Return to dashboard
    ];
    
    for (const path of navigationPaths) {
      await page.goto(path);
      await waitForPageLoad(page);
      
      // Verify we stay authenticated
      expect(page.url()).not.toContain('/auth/signin');
      expect(page.url()).toContain(path);
      
      // Wait a moment between navigations
      await page.waitForTimeout(1000);
    }
    
    // Take final screenshot
    await takeScreenshot(page, 'farbour-session-maintained');
  });

  test('should successfully sign out and require re-authentication', async ({ page }) => {
    // Sign in as admin
    await signInAsAdmin(page);
    
    // Verify we're authenticated
    expect(page.url()).toContain('/dashboard');
    
    // Sign out
    await signOut(page);
    
    // Verify we're redirected to sign-in page
    await page.waitForURL('**/auth/signin', { timeout: 10000 });
    
    // Verify sign-in page elements
    await expect(page.locator('h1:has-text("NOLK")')).toBeVisible();
    
    // Try to access protected page - should redirect to sign-in
    await page.goto('/admin');
    await waitForPageLoad(page);
    
    // Should be redirected back to sign-in
    expect(page.url()).toContain('/auth/signin');
    
    // Take screenshot of signed-out state
    await takeScreenshot(page, 'farbour-signed-out');
  });

  test('should handle invalid credentials appropriately', async ({ page }) => {
    // Navigate to sign-in page
    await page.goto('/auth/signin');
    await waitForPageLoad(page);
    
    // Try to sign in with wrong password
    try {
      await signInWithCredentials(page, 'farbour', 'wrongpassword');
      // If we get here without error, the test should fail
      expect(false).toBe(true);
    } catch (error) {
      // Expected to fail - verify we're still on sign-in page
      expect(page.url()).toContain('/auth/signin');
    }
    
    // Try to sign in with wrong username
    try {
      await signInWithCredentials(page, 'wronguser', 'admin');
      // If we get here without error, the test should fail
      expect(false).toBe(true);
    } catch (error) {
      // Expected to fail - verify we're still on sign-in page
      expect(page.url()).toContain('/auth/signin');
    }
    
    // Take screenshot of failed login attempts
    await takeScreenshot(page, 'farbour-invalid-credentials');
  });

  test('should test complete user workflow', async ({ page }) => {
    // Complete workflow test
    
    // 1. Sign in
    await signInAsAdmin(page);
    await takeScreenshot(page, 'farbour-workflow-1-signin');
    
    // 2. Visit dashboard
    await page.goto('/dashboard');
    await waitForPageLoad(page);
    await takeScreenshot(page, 'farbour-workflow-2-dashboard');
    
    // 3. Visit marketing section
    await page.goto('/marketing');
    await waitForPageLoad(page);
    await takeScreenshot(page, 'farbour-workflow-3-marketing');
    
    // 4. Visit admin section
    await page.goto('/admin');
    await waitForPageLoad(page);
    await takeScreenshot(page, 'farbour-workflow-4-admin');
    
    // 5. Return to dashboard
    await page.goto('/dashboard');
    await waitForPageLoad(page);
    await takeScreenshot(page, 'farbour-workflow-5-return-dashboard');
    
    // 6. Sign out
    await signOut(page);
    await takeScreenshot(page, 'farbour-workflow-6-signout');
    
    // Verify complete workflow
    expect(page.url()).toContain('/auth/signin');
  });

  test('should verify responsive design works for admin user', async ({ page }) => {
    // Sign in as admin
    await signInAsAdmin(page);
    
    // Test different viewport sizes
    const viewports = [
      { width: 375, height: 667, name: 'mobile' },
      { width: 768, height: 1024, name: 'tablet' },
      { width: 1920, height: 1080, name: 'desktop' }
    ];
    
    for (const viewport of viewports) {
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.waitForTimeout(1000);
      
      // Test dashboard
      await page.goto('/dashboard');
      await waitForPageLoad(page);
      await takeScreenshot(page, `farbour-responsive-dashboard-${viewport.name}`);
      
      // Test admin page
      await page.goto('/admin');
      await waitForPageLoad(page);
      await takeScreenshot(page, `farbour-responsive-admin-${viewport.name}`);
    }
  });

  test('should verify no console errors during admin usage', async ({ page }) => {
    const consoleErrors: string[] = [];
    
    // Capture console errors
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    // Sign in and navigate through the app
    await signInAsAdmin(page);
    
    // Visit main sections
    const sections = ['/dashboard', '/marketing', '/admin'];
    
    for (const section of sections) {
      await page.goto(section);
      await waitForPageLoad(page);
      await page.waitForTimeout(2000); // Wait for any async operations
    }
    
    // Filter out acceptable errors
    const criticalErrors = consoleErrors.filter(error =>
      !error.includes('favicon') &&
      !error.includes('404') &&
      !error.includes('net::ERR_FAILED') &&
      !error.includes('chrome-extension')
    );
    
    // Report any critical errors
    if (criticalErrors.length > 0) {
      console.log('Console errors found:', criticalErrors);
    }
    
    expect(criticalErrors).toHaveLength(0);
  });
});
