{"config": {"configFile": "/Users/<USER>/Projects/NOLK/nolk-v4/playwright.config.ts", "rootDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/global-setup.ts", "globalTeardown": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 8}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "tests/reports/e2e-html"}], ["json", {"outputFile": "tests/reports/e2e-results.json"}], ["junit", {"outputFile": "tests/reports/e2e-junit.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 8, "webServer": {"command": "npm run dev", "url": "http://localhost:6699", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "farbour-admin-test.spec.ts", "file": "farbour-admin-test.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Farbour Admin User - Complete Functionality Test", "file": "farbour-admin-test.spec.ts", "line": 19, "column": 6, "specs": [{"title": "should successfully authenticate farbour user with admin credentials", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "timedOut", "duration": 30579, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:34:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:57:04.231Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-chromium/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-chromium/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-360af8a07f0fc939916e", "file": "farbour-admin-test.spec.ts", "line": 25, "column": 7}, {"title": "should have admin access to all sections", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "timedOut", "duration": 30330, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:49:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:57:04.226Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-chromium/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-chromium/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-a07f36e470c9ad723c91", "file": "farbour-admin-test.spec.ts", "line": 47, "column": 7}, {"title": "should verify admin privileges and permissions", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "timedOut", "duration": 30648, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:73:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:57:04.224Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-chromium/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-chromium/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-3fd2b05021bdd50e2490", "file": "farbour-admin-test.spec.ts", "line": 71, "column": 7}, {"title": "should maintain session across navigation", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "timedOut", "duration": 30266, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:93:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:57:04.226Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-chromium/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-chromium/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-b35e86573525848248a5", "file": "farbour-admin-test.spec.ts", "line": 91, "column": 7}, {"title": "should successfully sign out and require re-authentication", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "timedOut", "duration": 30483, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:121:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:57:04.227Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-chromium/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-chromium/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-1ac9268debc5ed44cb92", "file": "farbour-admin-test.spec.ts", "line": 119, "column": 7}, {"title": "should handle invalid credentials appropriately", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "timedOut", "duration": 30745, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 124}, "message": "Error: page.screenshot: Target page, context or browser has been closed\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:124\n\n\u001b[0m \u001b[90m 122 |\u001b[39m \u001b[90m */\u001b[39m\n \u001b[90m 123 |\u001b[39m \u001b[36mexport\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m takeScreenshot(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m name\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 124 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mscreenshot({\n \u001b[90m     |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 125 |\u001b[39m     path\u001b[33m:\u001b[39m \u001b[32m`tests/reports/e2e-artifacts/screenshots/${name}-${Date.now()}.png`\u001b[39m\u001b[33m,\u001b[39m\n \u001b[90m 126 |\u001b[39m     fullPage\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\n \u001b[90m 127 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at takeScreenshot (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:124:14)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:172:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:57:04.226Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-chromium/video.webm"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-305218979f96967eb91b", "file": "farbour-admin-test.spec.ts", "line": 146, "column": 7}, {"title": "should test complete user workflow", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "timedOut", "duration": 30356, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:179:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:57:04.229Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-chromium/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-chromium/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-a5d3132637bd68e26948", "file": "farbour-admin-test.spec.ts", "line": 175, "column": 7}, {"title": "should verify responsive design works for admin user", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "timedOut", "duration": 30807, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:212:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:57:04.229Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-chromium/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-chromium/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-9b6b626225dbcbbbe0a4", "file": "farbour-admin-test.spec.ts", "line": 210, "column": 7}, {"title": "should verify no console errors during admin usage", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 3, "status": "timedOut", "duration": 30524, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:248:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:57:36.596Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-chromium/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-chromium/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-3b0f80cdefa89ebf4c3a", "file": "farbour-admin-test.spec.ts", "line": 237, "column": 7}, {"title": "should successfully authenticate farbour user with admin credentials", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 9, "parallelIndex": 6, "status": "timedOut", "duration": 30097, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:34:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:57:36.631Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-firefox/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-firefox/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-firefox/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-a08465c5c886d6f936f6", "file": "farbour-admin-test.spec.ts", "line": 25, "column": 7}, {"title": "should have admin access to all sections", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 10, "parallelIndex": 1, "status": "timedOut", "duration": 30103, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:49:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:57:36.648Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-firefox/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-firefox/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-firefox/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-329e3d161a653ae9713a", "file": "farbour-admin-test.spec.ts", "line": 47, "column": 7}, {"title": "should verify admin privileges and permissions", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 11, "parallelIndex": 4, "status": "timedOut", "duration": 30098, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:73:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:57:36.787Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-firefox/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-firefox/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-firefox/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-32b118ffbf5dcb5eab16", "file": "farbour-admin-test.spec.ts", "line": 71, "column": 7}, {"title": "should maintain session across navigation", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "timedOut", "duration": 30099, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:93:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:57:37.046Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-firefox/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-firefox/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-firefox/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-89de4f506f35601fbf8a", "file": "farbour-admin-test.spec.ts", "line": 91, "column": 7}, {"title": "should successfully sign out and require re-authentication", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 13, "parallelIndex": 2, "status": "timedOut", "duration": 30136, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:121:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:57:37.055Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-firefox/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-firefox/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-firefox/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-1eb145df715b6e304543", "file": "farbour-admin-test.spec.ts", "line": 119, "column": 7}, {"title": "should handle invalid credentials appropriately", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 14, "parallelIndex": 7, "status": "timedOut", "duration": 30091, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 124}, "message": "Error: page.screenshot: Target page, context or browser has been closed\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:124\n\n\u001b[0m \u001b[90m 122 |\u001b[39m \u001b[90m */\u001b[39m\n \u001b[90m 123 |\u001b[39m \u001b[36mexport\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m takeScreenshot(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m name\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 124 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mscreenshot({\n \u001b[90m     |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 125 |\u001b[39m     path\u001b[33m:\u001b[39m \u001b[32m`tests/reports/e2e-artifacts/screenshots/${name}-${Date.now()}.png`\u001b[39m\u001b[33m,\u001b[39m\n \u001b[90m 126 |\u001b[39m     fullPage\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\n \u001b[90m 127 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at takeScreenshot (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:124:14)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:172:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:57:37.200Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-firefox/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-firefox/video.webm"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-32ac73b9891a724e4831", "file": "farbour-admin-test.spec.ts", "line": 146, "column": 7}, {"title": "should test complete user workflow", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 15, "parallelIndex": 5, "status": "timedOut", "duration": 30130, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:179:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:57:37.422Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-firefox/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-firefox/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-firefox/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-721926dad4ec239b5163", "file": "farbour-admin-test.spec.ts", "line": 175, "column": 7}, {"title": "should verify responsive design works for admin user", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 16, "parallelIndex": 3, "status": "timedOut", "duration": 30095, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:212:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:58:07.820Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-firefox/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-firefox/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-firefox/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-6ab888f78cfbb8cd8ef9", "file": "farbour-admin-test.spec.ts", "line": 210, "column": 7}, {"title": "should verify no console errors during admin usage", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 17, "parallelIndex": 7, "status": "timedOut", "duration": 30099, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:248:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:58:08.914Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-firefox/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-firefox/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-firefox/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-d8b4879dfbaaf081fad6", "file": "farbour-admin-test.spec.ts", "line": 237, "column": 7}, {"title": "should successfully authenticate farbour user with admin credentials", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 18, "parallelIndex": 0, "status": "timedOut", "duration": 30078, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:34:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:58:09.340Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-webkit/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-webkit/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-webkit/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-575e28a98abebe8f83ff", "file": "farbour-admin-test.spec.ts", "line": 25, "column": 7}, {"title": "should have admin access to all sections", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 19, "parallelIndex": 4, "status": "timedOut", "duration": 30079, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:49:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:58:09.606Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-webkit/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-webkit/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-webkit/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-9f2d60682fe5886ff9af", "file": "farbour-admin-test.spec.ts", "line": 47, "column": 7}, {"title": "should verify admin privileges and permissions", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 20, "parallelIndex": 5, "status": "timedOut", "duration": 30081, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:73:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:58:09.976Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-webkit/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-webkit/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-webkit/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-3d65806cef25364161bf", "file": "farbour-admin-test.spec.ts", "line": 71, "column": 7}, {"title": "should maintain session across navigation", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 21, "parallelIndex": 6, "status": "timedOut", "duration": 30080, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:93:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:58:10.309Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-webkit/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-webkit/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-webkit/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-e6a9d0197f02f7d2b3c7", "file": "farbour-admin-test.spec.ts", "line": 91, "column": 7}, {"title": "should successfully sign out and require re-authentication", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 22, "parallelIndex": 2, "status": "timedOut", "duration": 30078, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:121:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:58:10.766Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-webkit/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-webkit/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-webkit/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-5c0eef35fe9b43805cc2", "file": "farbour-admin-test.spec.ts", "line": 119, "column": 7}, {"title": "should handle invalid credentials appropriately", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 23, "parallelIndex": 1, "status": "timedOut", "duration": 30080, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 124}, "message": "Error: page.screenshot: Target page, context or browser has been closed\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:124\n\n\u001b[0m \u001b[90m 122 |\u001b[39m \u001b[90m */\u001b[39m\n \u001b[90m 123 |\u001b[39m \u001b[36mexport\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m takeScreenshot(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m name\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 124 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mscreenshot({\n \u001b[90m     |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 125 |\u001b[39m     path\u001b[33m:\u001b[39m \u001b[32m`tests/reports/e2e-artifacts/screenshots/${name}-${Date.now()}.png`\u001b[39m\u001b[33m,\u001b[39m\n \u001b[90m 126 |\u001b[39m     fullPage\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\n \u001b[90m 127 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at takeScreenshot (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:124:14)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:172:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:58:11.070Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-webkit/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-webkit/video.webm"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-ab9a7fca613427648b72", "file": "farbour-admin-test.spec.ts", "line": 146, "column": 7}, {"title": "should test complete user workflow", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 24, "parallelIndex": 3, "status": "timedOut", "duration": 30109, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:179:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:58:38.847Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-webkit/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-webkit/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-webkit/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-8c3095f0bea0e942741c", "file": "farbour-admin-test.spec.ts", "line": 175, "column": 7}, {"title": "should verify responsive design works for admin user", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 25, "parallelIndex": 7, "status": "timedOut", "duration": 30075, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:212:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:58:40.021Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-webkit/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-webkit/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-webkit/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-706a06a6894fc4ae9cf6", "file": "farbour-admin-test.spec.ts", "line": 210, "column": 7}, {"title": "should verify no console errors during admin usage", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 29, "parallelIndex": 0, "status": "timedOut", "duration": 30074, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:248:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:58:42.559Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-webkit/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-webkit/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-webkit/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-be3f26ac0260f58a72df", "file": "farbour-admin-test.spec.ts", "line": 237, "column": 7}, {"title": "should successfully authenticate farbour user with admin credentials", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 26, "parallelIndex": 1, "status": "timedOut", "duration": 30164, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:34:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:58:42.562Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-Mobile-Chrome/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-Mobile-Chrome/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-0dcdc561295cb20d44fb", "file": "farbour-admin-test.spec.ts", "line": 25, "column": 7}, {"title": "should have admin access to all sections", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 27, "parallelIndex": 6, "status": "timedOut", "duration": 30214, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:49:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:58:42.561Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-Mobile-Chrome/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-Mobile-Chrome/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-aed7a90bed3c73975f97", "file": "farbour-admin-test.spec.ts", "line": 47, "column": 7}, {"title": "should verify admin privileges and permissions", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 28, "parallelIndex": 2, "status": "timedOut", "duration": 30191, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:73:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:58:42.559Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-Mobile-Chrome/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-Mobile-Chrome/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-ca94297f0566e498fd9c", "file": "farbour-admin-test.spec.ts", "line": 71, "column": 7}, {"title": "should maintain session across navigation", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 30, "parallelIndex": 5, "status": "timedOut", "duration": 30220, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:93:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:58:42.560Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-Mobile-Chrome/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-Mobile-Chrome/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-7cf80b7ebffcea4224ed", "file": "farbour-admin-test.spec.ts", "line": 91, "column": 7}, {"title": "should successfully sign out and require re-authentication", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 31, "parallelIndex": 4, "status": "timedOut", "duration": 30214, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:121:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:58:42.580Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-Mobile-Chrome/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-Mobile-Chrome/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-ee4b676ffed9fcd0258b", "file": "farbour-admin-test.spec.ts", "line": 119, "column": 7}, {"title": "should handle invalid credentials appropriately", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 32, "parallelIndex": 3, "status": "timedOut", "duration": 30516, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 124}, "message": "Error: page.screenshot: Target page, context or browser has been closed\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:124\n\n\u001b[0m \u001b[90m 122 |\u001b[39m \u001b[90m */\u001b[39m\n \u001b[90m 123 |\u001b[39m \u001b[36mexport\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m takeScreenshot(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m name\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 124 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mscreenshot({\n \u001b[90m     |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 125 |\u001b[39m     path\u001b[33m:\u001b[39m \u001b[32m`tests/reports/e2e-artifacts/screenshots/${name}-${Date.now()}.png`\u001b[39m\u001b[33m,\u001b[39m\n \u001b[90m 126 |\u001b[39m     fullPage\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\n \u001b[90m 127 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at takeScreenshot (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:124:14)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:172:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:59:09.458Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-Mobile-Chrome/video.webm"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-7b76bb1008e3313ca32a", "file": "farbour-admin-test.spec.ts", "line": 146, "column": 7}, {"title": "should test complete user workflow", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 33, "parallelIndex": 7, "status": "timedOut", "duration": 30181, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:179:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:59:10.556Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-Mobile-Chrome/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-Mobile-Chrome/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-769fb3c198ce319071fe", "file": "farbour-admin-test.spec.ts", "line": 175, "column": 7}, {"title": "should verify responsive design works for admin user", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 34, "parallelIndex": 0, "status": "timedOut", "duration": 30094, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:212:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:59:13.167Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-Mobile-Chrome/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-Mobile-Chrome/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-86949e869b6401b74ff1", "file": "farbour-admin-test.spec.ts", "line": 210, "column": 7}, {"title": "should verify no console errors during admin usage", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 35, "parallelIndex": 1, "status": "timedOut", "duration": 30108, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:248:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:59:13.804Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-Mobile-Chrome/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-Mobile-Chrome/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-Mobile-Chrome/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-6cc25e0d1ffb7c216c4e", "file": "farbour-admin-test.spec.ts", "line": 237, "column": 7}, {"title": "should successfully authenticate farbour user with admin credentials", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 36, "parallelIndex": 4, "status": "timedOut", "duration": 30051, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:34:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:59:13.893Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-Mobile-Safari/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-Mobile-Safari/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-Mobile-Safari/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-c6200cd70e1a8bb9c8b0", "file": "farbour-admin-test.spec.ts", "line": 25, "column": 7}, {"title": "should have admin access to all sections", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 37, "parallelIndex": 2, "status": "timedOut", "duration": 30066, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:49:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:59:13.932Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-Mobile-Safari/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-Mobile-Safari/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-Mobile-Safari/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-5f3444dbdf371c8c7f72", "file": "farbour-admin-test.spec.ts", "line": 47, "column": 7}, {"title": "should verify admin privileges and permissions", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 38, "parallelIndex": 6, "status": "timedOut", "duration": 30071, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:73:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:59:13.929Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-Mobile-Safari/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-Mobile-Safari/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-Mobile-Safari/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-63a1360d7d335016e40b", "file": "farbour-admin-test.spec.ts", "line": 71, "column": 7}, {"title": "should maintain session across navigation", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 39, "parallelIndex": 5, "status": "timedOut", "duration": 30072, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:93:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:59:13.943Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-Mobile-Safari/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-Mobile-Safari/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-Mobile-Safari/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-c85b83bb7c938d3e0854", "file": "farbour-admin-test.spec.ts", "line": 91, "column": 7}, {"title": "should successfully sign out and require re-authentication", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 40, "parallelIndex": 3, "status": "timedOut", "duration": 30068, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:121:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:59:40.614Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-Mobile-Safari/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-Mobile-Safari/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-Mobile-Safari/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-678fae4e7087f1193f10", "file": "farbour-admin-test.spec.ts", "line": 119, "column": 7}, {"title": "should handle invalid credentials appropriately", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 41, "parallelIndex": 7, "status": "timedOut", "duration": 30050, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 124}, "message": "Error: page.screenshot: Target page, context or browser has been closed\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:124\n\n\u001b[0m \u001b[90m 122 |\u001b[39m \u001b[90m */\u001b[39m\n \u001b[90m 123 |\u001b[39m \u001b[36mexport\u001b[39m \u001b[36masync\u001b[39m \u001b[36mfunction\u001b[39m takeScreenshot(page\u001b[33m:\u001b[39m \u001b[33mPage\u001b[39m\u001b[33m,\u001b[39m name\u001b[33m:\u001b[39m string) {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 124 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mscreenshot({\n \u001b[90m     |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 125 |\u001b[39m     path\u001b[33m:\u001b[39m \u001b[32m`tests/reports/e2e-artifacts/screenshots/${name}-${Date.now()}.png`\u001b[39m\u001b[33m,\u001b[39m\n \u001b[90m 126 |\u001b[39m     fullPage\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m\n \u001b[90m 127 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at takeScreenshot (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:124:14)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:172:25\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:59:41.345Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-Mobile-Safari/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-Mobile-Safari/video.webm"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-c1d657f77d254db28f31", "file": "farbour-admin-test.spec.ts", "line": 146, "column": 7}, {"title": "should test complete user workflow", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 42, "parallelIndex": 0, "status": "timedOut", "duration": 30066, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:179:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:59:44.000Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-Mobile-Safari/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-Mobile-Safari/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-Mobile-Safari/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-133fb05587f9aa582fbc", "file": "farbour-admin-test.spec.ts", "line": 175, "column": 7}, {"title": "should verify responsive design works for admin user", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 43, "parallelIndex": 4, "status": "timedOut", "duration": 30057, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:212:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:59:44.512Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-Mobile-Safari/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-Mobile-Safari/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-Mobile-Safari/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-63129473c599aeeee44c", "file": "farbour-admin-test.spec.ts", "line": 210, "column": 7}, {"title": "should verify no console errors during admin usage", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 44, "parallelIndex": 5, "status": "timedOut", "duration": 30068, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts", "column": 14, "line": 48}, "message": "Error: page.fill: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('input[name=\"username\"]')\u001b[22m\n\n\n\u001b[90m   at \u001b[39mutils/test-helpers.ts:48\n\n\u001b[0m \u001b[90m 46 |\u001b[39m\n \u001b[90m 47 |\u001b[39m   \u001b[90m// Fill in credentials\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 48 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"username\"]'\u001b[39m\u001b[33m,\u001b[39m username)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m              \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 49 |\u001b[39m   \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mfill(\u001b[32m'input[name=\"password\"]'\u001b[39m\u001b[33m,\u001b[39m password)\u001b[33m;\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m   \u001b[90m// Click sign in button\u001b[39m\u001b[0m\n\u001b[2m    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)\u001b[22m\n\u001b[2m    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)\u001b[22m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:248:5\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-30T00:59:44.570Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-Mobile-Safari/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-Mobile-Safari/video.webm"}, {"name": "_error-context-1", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-Mobile-Safari/error-context.md"}]}], "status": "unexpected"}], "id": "a5eb2f8a5375e554799c-9af8a065774a50f2547f", "file": "farbour-admin-test.spec.ts", "line": 237, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-05-30T00:57:00.569Z", "duration": 194209.82, "expected": 0, "skipped": 0, "unexpected": 45, "flaky": 0}}