<testsuites id="" name="" tests="45" failures="45" skipped="0" errors="0" time="194.20982">
<testsuite name="farbour-admin-test.spec.ts" timestamp="2025-05-30T00:57:03.982Z" hostname="chromium" tests="9" failures="9" skipped="0" time="274.738" errors="0">
<testcase name="Farbour Admin User - Complete Functionality Test › should successfully authenticate farbour user with admin credentials" classname="farbour-admin-test.spec.ts" time="30.579">
<failure message="farbour-admin-test.spec.ts:25:7 should successfully authenticate farbour user with admin credentials" type="FAILURE">
<![CDATA[  [chromium] › farbour-admin-test.spec.ts:25:7 › Farbour Admin User - Complete Functionality Test › should successfully authenticate farbour user with admin credentials 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:34:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should have admin access to all sections" classname="farbour-admin-test.spec.ts" time="30.33">
<failure message="farbour-admin-test.spec.ts:47:7 should have admin access to all sections" type="FAILURE">
<![CDATA[  [chromium] › farbour-admin-test.spec.ts:47:7 › Farbour Admin User - Complete Functionality Test › should have admin access to all sections 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:49:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should verify admin privileges and permissions" classname="farbour-admin-test.spec.ts" time="30.648">
<failure message="farbour-admin-test.spec.ts:71:7 should verify admin privileges and permissions" type="FAILURE">
<![CDATA[  [chromium] › farbour-admin-test.spec.ts:71:7 › Farbour Admin User - Complete Functionality Test › should verify admin privileges and permissions 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:73:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should maintain session across navigation" classname="farbour-admin-test.spec.ts" time="30.266">
<failure message="farbour-admin-test.spec.ts:91:7 should maintain session across navigation" type="FAILURE">
<![CDATA[  [chromium] › farbour-admin-test.spec.ts:91:7 › Farbour Admin User - Complete Functionality Test › should maintain session across navigation 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:93:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should successfully sign out and require re-authentication" classname="farbour-admin-test.spec.ts" time="30.483">
<failure message="farbour-admin-test.spec.ts:119:7 should successfully sign out and require re-authentication" type="FAILURE">
<![CDATA[  [chromium] › farbour-admin-test.spec.ts:119:7 › Farbour Admin User - Complete Functionality Test › should successfully sign out and require re-authentication 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:121:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should handle invalid credentials appropriately" classname="farbour-admin-test.spec.ts" time="30.745">
<failure message="farbour-admin-test.spec.ts:146:7 should handle invalid credentials appropriately" type="FAILURE">
<![CDATA[  [chromium] › farbour-admin-test.spec.ts:146:7 › Farbour Admin User - Complete Functionality Test › should handle invalid credentials appropriately 

    Test timeout of 30000ms exceeded.

    Error: page.screenshot: Target page, context or browser has been closed

       at utils/test-helpers.ts:124

      122 |  */
      123 | export async function takeScreenshot(page: Page, name: string) {
    > 124 |   await page.screenshot({
          |              ^
      125 |     path: `tests/reports/e2e-artifacts/screenshots/${name}-${Date.now()}.png`,
      126 |     fullPage: true
      127 |   });
        at takeScreenshot (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:124:14)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:172:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-chromium/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should test complete user workflow" classname="farbour-admin-test.spec.ts" time="30.356">
<failure message="farbour-admin-test.spec.ts:175:7 should test complete user workflow" type="FAILURE">
<![CDATA[  [chromium] › farbour-admin-test.spec.ts:175:7 › Farbour Admin User - Complete Functionality Test › should test complete user workflow 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:179:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should verify responsive design works for admin user" classname="farbour-admin-test.spec.ts" time="30.807">
<failure message="farbour-admin-test.spec.ts:210:7 should verify responsive design works for admin user" type="FAILURE">
<![CDATA[  [chromium] › farbour-admin-test.spec.ts:210:7 › Farbour Admin User - Complete Functionality Test › should verify responsive design works for admin user 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:212:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should verify no console errors during admin usage" classname="farbour-admin-test.spec.ts" time="30.524">
<failure message="farbour-admin-test.spec.ts:237:7 should verify no console errors during admin usage" type="FAILURE">
<![CDATA[  [chromium] › farbour-admin-test.spec.ts:237:7 › Farbour Admin User - Complete Functionality Test › should verify no console errors during admin usage 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:248:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="farbour-admin-test.spec.ts" timestamp="2025-05-30T00:57:03.982Z" hostname="firefox" tests="9" failures="9" skipped="0" time="270.948" errors="0">
<testcase name="Farbour Admin User - Complete Functionality Test › should successfully authenticate farbour user with admin credentials" classname="farbour-admin-test.spec.ts" time="30.097">
<failure message="farbour-admin-test.spec.ts:25:7 should successfully authenticate farbour user with admin credentials" type="FAILURE">
<![CDATA[  [firefox] › farbour-admin-test.spec.ts:25:7 › Farbour Admin User - Complete Functionality Test › should successfully authenticate farbour user with admin credentials 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:34:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-firefox/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-firefox/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should have admin access to all sections" classname="farbour-admin-test.spec.ts" time="30.103">
<failure message="farbour-admin-test.spec.ts:47:7 should have admin access to all sections" type="FAILURE">
<![CDATA[  [firefox] › farbour-admin-test.spec.ts:47:7 › Farbour Admin User - Complete Functionality Test › should have admin access to all sections 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:49:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-firefox/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-firefox/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should verify admin privileges and permissions" classname="farbour-admin-test.spec.ts" time="30.098">
<failure message="farbour-admin-test.spec.ts:71:7 should verify admin privileges and permissions" type="FAILURE">
<![CDATA[  [firefox] › farbour-admin-test.spec.ts:71:7 › Farbour Admin User - Complete Functionality Test › should verify admin privileges and permissions 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:73:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-firefox/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-firefox/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should maintain session across navigation" classname="farbour-admin-test.spec.ts" time="30.099">
<failure message="farbour-admin-test.spec.ts:91:7 should maintain session across navigation" type="FAILURE">
<![CDATA[  [firefox] › farbour-admin-test.spec.ts:91:7 › Farbour Admin User - Complete Functionality Test › should maintain session across navigation 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:93:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-firefox/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-firefox/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should successfully sign out and require re-authentication" classname="farbour-admin-test.spec.ts" time="30.136">
<failure message="farbour-admin-test.spec.ts:119:7 should successfully sign out and require re-authentication" type="FAILURE">
<![CDATA[  [firefox] › farbour-admin-test.spec.ts:119:7 › Farbour Admin User - Complete Functionality Test › should successfully sign out and require re-authentication 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:121:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-firefox/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-firefox/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should handle invalid credentials appropriately" classname="farbour-admin-test.spec.ts" time="30.091">
<failure message="farbour-admin-test.spec.ts:146:7 should handle invalid credentials appropriately" type="FAILURE">
<![CDATA[  [firefox] › farbour-admin-test.spec.ts:146:7 › Farbour Admin User - Complete Functionality Test › should handle invalid credentials appropriately 

    Test timeout of 30000ms exceeded.

    Error: page.screenshot: Target page, context or browser has been closed

       at utils/test-helpers.ts:124

      122 |  */
      123 | export async function takeScreenshot(page: Page, name: string) {
    > 124 |   await page.screenshot({
          |              ^
      125 |     path: `tests/reports/e2e-artifacts/screenshots/${name}-${Date.now()}.png`,
      126 |     fullPage: true
      127 |   });
        at takeScreenshot (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:124:14)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:172:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-firefox/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-firefox/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should test complete user workflow" classname="farbour-admin-test.spec.ts" time="30.13">
<failure message="farbour-admin-test.spec.ts:175:7 should test complete user workflow" type="FAILURE">
<![CDATA[  [firefox] › farbour-admin-test.spec.ts:175:7 › Farbour Admin User - Complete Functionality Test › should test complete user workflow 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:179:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-firefox/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-firefox/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should verify responsive design works for admin user" classname="farbour-admin-test.spec.ts" time="30.095">
<failure message="farbour-admin-test.spec.ts:210:7 should verify responsive design works for admin user" type="FAILURE">
<![CDATA[  [firefox] › farbour-admin-test.spec.ts:210:7 › Farbour Admin User - Complete Functionality Test › should verify responsive design works for admin user 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:212:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-firefox/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-firefox/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-firefox/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should verify no console errors during admin usage" classname="farbour-admin-test.spec.ts" time="30.099">
<failure message="farbour-admin-test.spec.ts:237:7 should verify no console errors during admin usage" type="FAILURE">
<![CDATA[  [firefox] › farbour-admin-test.spec.ts:237:7 › Farbour Admin User - Complete Functionality Test › should verify no console errors during admin usage 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:248:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-firefox/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-firefox/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-firefox/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-firefox/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-firefox/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-firefox/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="farbour-admin-test.spec.ts" timestamp="2025-05-30T00:57:03.982Z" hostname="webkit" tests="9" failures="9" skipped="0" time="270.734" errors="0">
<testcase name="Farbour Admin User - Complete Functionality Test › should successfully authenticate farbour user with admin credentials" classname="farbour-admin-test.spec.ts" time="30.078">
<failure message="farbour-admin-test.spec.ts:25:7 should successfully authenticate farbour user with admin credentials" type="FAILURE">
<![CDATA[  [webkit] › farbour-admin-test.spec.ts:25:7 › Farbour Admin User - Complete Functionality Test › should successfully authenticate farbour user with admin credentials 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:34:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-webkit/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-webkit/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-webkit/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should have admin access to all sections" classname="farbour-admin-test.spec.ts" time="30.079">
<failure message="farbour-admin-test.spec.ts:47:7 should have admin access to all sections" type="FAILURE">
<![CDATA[  [webkit] › farbour-admin-test.spec.ts:47:7 › Farbour Admin User - Complete Functionality Test › should have admin access to all sections 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:49:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-webkit/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-webkit/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-webkit/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should verify admin privileges and permissions" classname="farbour-admin-test.spec.ts" time="30.081">
<failure message="farbour-admin-test.spec.ts:71:7 should verify admin privileges and permissions" type="FAILURE">
<![CDATA[  [webkit] › farbour-admin-test.spec.ts:71:7 › Farbour Admin User - Complete Functionality Test › should verify admin privileges and permissions 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:73:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-webkit/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-webkit/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-webkit/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should maintain session across navigation" classname="farbour-admin-test.spec.ts" time="30.08">
<failure message="farbour-admin-test.spec.ts:91:7 should maintain session across navigation" type="FAILURE">
<![CDATA[  [webkit] › farbour-admin-test.spec.ts:91:7 › Farbour Admin User - Complete Functionality Test › should maintain session across navigation 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:93:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-webkit/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-webkit/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-webkit/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should successfully sign out and require re-authentication" classname="farbour-admin-test.spec.ts" time="30.078">
<failure message="farbour-admin-test.spec.ts:119:7 should successfully sign out and require re-authentication" type="FAILURE">
<![CDATA[  [webkit] › farbour-admin-test.spec.ts:119:7 › Farbour Admin User - Complete Functionality Test › should successfully sign out and require re-authentication 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:121:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-webkit/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-webkit/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-webkit/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should handle invalid credentials appropriately" classname="farbour-admin-test.spec.ts" time="30.08">
<failure message="farbour-admin-test.spec.ts:146:7 should handle invalid credentials appropriately" type="FAILURE">
<![CDATA[  [webkit] › farbour-admin-test.spec.ts:146:7 › Farbour Admin User - Complete Functionality Test › should handle invalid credentials appropriately 

    Test timeout of 30000ms exceeded.

    Error: page.screenshot: Target page, context or browser has been closed

       at utils/test-helpers.ts:124

      122 |  */
      123 | export async function takeScreenshot(page: Page, name: string) {
    > 124 |   await page.screenshot({
          |              ^
      125 |     path: `tests/reports/e2e-artifacts/screenshots/${name}-${Date.now()}.png`,
      126 |     fullPage: true
      127 |   });
        at takeScreenshot (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:124:14)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:172:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-webkit/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-webkit/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-webkit/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should test complete user workflow" classname="farbour-admin-test.spec.ts" time="30.109">
<failure message="farbour-admin-test.spec.ts:175:7 should test complete user workflow" type="FAILURE">
<![CDATA[  [webkit] › farbour-admin-test.spec.ts:175:7 › Farbour Admin User - Complete Functionality Test › should test complete user workflow 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:179:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-webkit/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-webkit/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-webkit/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should verify responsive design works for admin user" classname="farbour-admin-test.spec.ts" time="30.075">
<failure message="farbour-admin-test.spec.ts:210:7 should verify responsive design works for admin user" type="FAILURE">
<![CDATA[  [webkit] › farbour-admin-test.spec.ts:210:7 › Farbour Admin User - Complete Functionality Test › should verify responsive design works for admin user 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:212:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-webkit/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-webkit/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-webkit/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-webkit/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should verify no console errors during admin usage" classname="farbour-admin-test.spec.ts" time="30.074">
<failure message="farbour-admin-test.spec.ts:237:7 should verify no console errors during admin usage" type="FAILURE">
<![CDATA[  [webkit] › farbour-admin-test.spec.ts:237:7 › Farbour Admin User - Complete Functionality Test › should verify no console errors during admin usage 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:248:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-webkit/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-webkit/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-webkit/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-webkit/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-webkit/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-webkit/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="farbour-admin-test.spec.ts" timestamp="2025-05-30T00:57:03.982Z" hostname="Mobile Chrome" tests="9" failures="9" skipped="0" time="271.902" errors="0">
<testcase name="Farbour Admin User - Complete Functionality Test › should successfully authenticate farbour user with admin credentials" classname="farbour-admin-test.spec.ts" time="30.164">
<failure message="farbour-admin-test.spec.ts:25:7 should successfully authenticate farbour user with admin credentials" type="FAILURE">
<![CDATA[  [Mobile Chrome] › farbour-admin-test.spec.ts:25:7 › Farbour Admin User - Complete Functionality Test › should successfully authenticate farbour user with admin credentials 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:34:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-Mobile-Chrome/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-Mobile-Chrome/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should have admin access to all sections" classname="farbour-admin-test.spec.ts" time="30.214">
<failure message="farbour-admin-test.spec.ts:47:7 should have admin access to all sections" type="FAILURE">
<![CDATA[  [Mobile Chrome] › farbour-admin-test.spec.ts:47:7 › Farbour Admin User - Complete Functionality Test › should have admin access to all sections 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:49:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-Mobile-Chrome/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-Mobile-Chrome/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should verify admin privileges and permissions" classname="farbour-admin-test.spec.ts" time="30.191">
<failure message="farbour-admin-test.spec.ts:71:7 should verify admin privileges and permissions" type="FAILURE">
<![CDATA[  [Mobile Chrome] › farbour-admin-test.spec.ts:71:7 › Farbour Admin User - Complete Functionality Test › should verify admin privileges and permissions 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:73:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-Mobile-Chrome/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-Mobile-Chrome/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should maintain session across navigation" classname="farbour-admin-test.spec.ts" time="30.22">
<failure message="farbour-admin-test.spec.ts:91:7 should maintain session across navigation" type="FAILURE">
<![CDATA[  [Mobile Chrome] › farbour-admin-test.spec.ts:91:7 › Farbour Admin User - Complete Functionality Test › should maintain session across navigation 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:93:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-Mobile-Chrome/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-Mobile-Chrome/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should successfully sign out and require re-authentication" classname="farbour-admin-test.spec.ts" time="30.214">
<failure message="farbour-admin-test.spec.ts:119:7 should successfully sign out and require re-authentication" type="FAILURE">
<![CDATA[  [Mobile Chrome] › farbour-admin-test.spec.ts:119:7 › Farbour Admin User - Complete Functionality Test › should successfully sign out and require re-authentication 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:121:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-Mobile-Chrome/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-Mobile-Chrome/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should handle invalid credentials appropriately" classname="farbour-admin-test.spec.ts" time="30.516">
<failure message="farbour-admin-test.spec.ts:146:7 should handle invalid credentials appropriately" type="FAILURE">
<![CDATA[  [Mobile Chrome] › farbour-admin-test.spec.ts:146:7 › Farbour Admin User - Complete Functionality Test › should handle invalid credentials appropriately 

    Test timeout of 30000ms exceeded.

    Error: page.screenshot: Target page, context or browser has been closed

       at utils/test-helpers.ts:124

      122 |  */
      123 | export async function takeScreenshot(page: Page, name: string) {
    > 124 |   await page.screenshot({
          |              ^
      125 |     path: `tests/reports/e2e-artifacts/screenshots/${name}-${Date.now()}.png`,
      126 |     fullPage: true
      127 |   });
        at takeScreenshot (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:124:14)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:172:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-Mobile-Chrome/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-Mobile-Chrome/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should test complete user workflow" classname="farbour-admin-test.spec.ts" time="30.181">
<failure message="farbour-admin-test.spec.ts:175:7 should test complete user workflow" type="FAILURE">
<![CDATA[  [Mobile Chrome] › farbour-admin-test.spec.ts:175:7 › Farbour Admin User - Complete Functionality Test › should test complete user workflow 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:179:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-Mobile-Chrome/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-Mobile-Chrome/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should verify responsive design works for admin user" classname="farbour-admin-test.spec.ts" time="30.094">
<failure message="farbour-admin-test.spec.ts:210:7 should verify responsive design works for admin user" type="FAILURE">
<![CDATA[  [Mobile Chrome] › farbour-admin-test.spec.ts:210:7 › Farbour Admin User - Complete Functionality Test › should verify responsive design works for admin user 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:212:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-Mobile-Chrome/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-Mobile-Chrome/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should verify no console errors during admin usage" classname="farbour-admin-test.spec.ts" time="30.108">
<failure message="farbour-admin-test.spec.ts:237:7 should verify no console errors during admin usage" type="FAILURE">
<![CDATA[  [Mobile Chrome] › farbour-admin-test.spec.ts:237:7 › Farbour Admin User - Complete Functionality Test › should verify no console errors during admin usage 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:248:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-Mobile-Chrome/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-Mobile-Chrome/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-Mobile-Chrome/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-Mobile-Chrome/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-Mobile-Chrome/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-Mobile-Chrome/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="farbour-admin-test.spec.ts" timestamp="2025-05-30T00:57:03.982Z" hostname="Mobile Safari" tests="9" failures="9" skipped="0" time="270.569" errors="0">
<testcase name="Farbour Admin User - Complete Functionality Test › should successfully authenticate farbour user with admin credentials" classname="farbour-admin-test.spec.ts" time="30.051">
<failure message="farbour-admin-test.spec.ts:25:7 should successfully authenticate farbour user with admin credentials" type="FAILURE">
<![CDATA[  [Mobile Safari] › farbour-admin-test.spec.ts:25:7 › Farbour Admin User - Complete Functionality Test › should successfully authenticate farbour user with admin credentials 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:34:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-Mobile-Safari/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-Mobile-Safari/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-3999e-user-with-admin-credentials-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should have admin access to all sections" classname="farbour-admin-test.spec.ts" time="30.066">
<failure message="farbour-admin-test.spec.ts:47:7 should have admin access to all sections" type="FAILURE">
<![CDATA[  [Mobile Safari] › farbour-admin-test.spec.ts:47:7 › Farbour Admin User - Complete Functionality Test › should have admin access to all sections 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:49:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-Mobile-Safari/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-Mobile-Safari/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-aed99-dmin-access-to-all-sections-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should verify admin privileges and permissions" classname="farbour-admin-test.spec.ts" time="30.071">
<failure message="farbour-admin-test.spec.ts:71:7 should verify admin privileges and permissions" type="FAILURE">
<![CDATA[  [Mobile Safari] › farbour-admin-test.spec.ts:71:7 › Farbour Admin User - Complete Functionality Test › should verify admin privileges and permissions 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:73:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-Mobile-Safari/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-Mobile-Safari/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-2f12d--privileges-and-permissions-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should maintain session across navigation" classname="farbour-admin-test.spec.ts" time="30.072">
<failure message="farbour-admin-test.spec.ts:91:7 should maintain session across navigation" type="FAILURE">
<![CDATA[  [Mobile Safari] › farbour-admin-test.spec.ts:91:7 › Farbour Admin User - Complete Functionality Test › should maintain session across navigation 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:93:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-Mobile-Safari/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-Mobile-Safari/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-e365b-n-session-across-navigation-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should successfully sign out and require re-authentication" classname="farbour-admin-test.spec.ts" time="30.068">
<failure message="farbour-admin-test.spec.ts:119:7 should successfully sign out and require re-authentication" type="FAILURE">
<![CDATA[  [Mobile Safari] › farbour-admin-test.spec.ts:119:7 › Farbour Admin User - Complete Functionality Test › should successfully sign out and require re-authentication 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:121:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-Mobile-Safari/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-Mobile-Safari/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-f36e9-d-require-re-authentication-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should handle invalid credentials appropriately" classname="farbour-admin-test.spec.ts" time="30.05">
<failure message="farbour-admin-test.spec.ts:146:7 should handle invalid credentials appropriately" type="FAILURE">
<![CDATA[  [Mobile Safari] › farbour-admin-test.spec.ts:146:7 › Farbour Admin User - Complete Functionality Test › should handle invalid credentials appropriately 

    Test timeout of 30000ms exceeded.

    Error: page.screenshot: Target page, context or browser has been closed

       at utils/test-helpers.ts:124

      122 |  */
      123 | export async function takeScreenshot(page: Page, name: string) {
    > 124 |   await page.screenshot({
          |              ^
      125 |     path: `tests/reports/e2e-artifacts/screenshots/${name}-${Date.now()}.png`,
      126 |     fullPage: true
      127 |   });
        at takeScreenshot (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:124:14)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:172:25

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-Mobile-Safari/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-577d7-d-credentials-appropriately-Mobile-Safari/video.webm]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should test complete user workflow" classname="farbour-admin-test.spec.ts" time="30.066">
<failure message="farbour-admin-test.spec.ts:175:7 should test complete user workflow" type="FAILURE">
<![CDATA[  [Mobile Safari] › farbour-admin-test.spec.ts:175:7 › Farbour Admin User - Complete Functionality Test › should test complete user workflow 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:179:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-Mobile-Safari/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-Mobile-Safari/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a1cc2-test-complete-user-workflow-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should verify responsive design works for admin user" classname="farbour-admin-test.spec.ts" time="30.057">
<failure message="farbour-admin-test.spec.ts:210:7 should verify responsive design works for admin user" type="FAILURE">
<![CDATA[  [Mobile Safari] › farbour-admin-test.spec.ts:210:7 › Farbour Admin User - Complete Functionality Test › should verify responsive design works for admin user 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:212:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-Mobile-Safari/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-Mobile-Safari/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-a25f8-design-works-for-admin-user-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Farbour Admin User - Complete Functionality Test › should verify no console errors during admin usage" classname="farbour-admin-test.spec.ts" time="30.068">
<failure message="farbour-admin-test.spec.ts:237:7 should verify no console errors during admin usage" type="FAILURE">
<![CDATA[  [Mobile Safari] › farbour-admin-test.spec.ts:237:7 › Farbour Admin User - Complete Functionality Test › should verify no console errors during admin usage 

    Test timeout of 30000ms exceeded.

    Error: page.fill: Test timeout of 30000ms exceeded.
    Call log:
      - waiting for locator('input[name="username"]')


       at utils/test-helpers.ts:48

      46 |
      47 |   // Fill in credentials
    > 48 |   await page.fill('input[name="username"]', username);
         |              ^
      49 |   await page.fill('input[name="password"]', password);
      50 |
      51 |   // Click sign in button
        at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
        at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:248:5

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-Mobile-Safari/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-Mobile-Safari/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-Mobile-Safari/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-Mobile-Safari/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-Mobile-Safari/video.webm]]

[[ATTACHMENT|e2e-artifacts/farbour-admin-test-Farbour-58b4a-e-errors-during-admin-usage-Mobile-Safari/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>