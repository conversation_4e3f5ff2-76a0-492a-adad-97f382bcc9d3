# Test info

- Name: Farbour Admin User - Complete Functionality Test >> should verify responsive design works for admin user
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:210:7

# Error details

```
Error: page.fill: Test timeout of 30000ms exceeded.
Call log:
  - waiting for locator('input[name="username"]')

    at signInWithCredentials (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:48:14)
    at signInAsAdmin (/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/utils/test-helpers.ts:63:3)
    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/farbour-admin-test.spec.ts:212:5
```

# Page snapshot

```yaml
- img
- heading "NOLK" [level=1]
- heading "Sign in to your account" [level=2]
- paragraph: Choose your preferred sign-in method
- button "Google"
- button "Username/Password"
- text: Username
- textbox "Username"
- text: Password
- textbox "Password"
- button "Sign In" [disabled]
- paragraph: By signing in, you agree to our terms of service and privacy policy.
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
   1 | import { Page, expect } from '@playwright/test';
   2 |
   3 | /**
   4 |  * Helper functions for E2E tests
   5 |  */
   6 |
   7 | /**
   8 |  * Wait for the page to be fully loaded
   9 |  */
   10 | export async function waitForPageLoad(page: Page) {
   11 |   await page.waitForLoadState('networkidle');
   12 |   await page.waitForLoadState('domcontentloaded');
   13 | }
   14 |
   15 | /**
   16 |  * Sign in with Google (mock for testing)
   17 |  */
   18 | export async function signInWithGoogle(page: Page) {
   19 |   // Navigate to sign-in page
   20 |   await page.goto('/auth/signin');
   21 |   await waitForPageLoad(page);
   22 |
   23 |   // Click the Google sign-in button
   24 |   await page.click('button:has-text("Continue with Google")');
   25 |
   26 |   // In a real test environment, you would handle OAuth flow
   27 |   // For now, we'll assume the sign-in is successful and we're redirected
   28 |   await page.waitForURL('/dashboard');
   29 |   await waitForPageLoad(page);
   30 | }
   31 |
   32 | /**
   33 |  * Sign in with username and password credentials
   34 |  */
   35 | export async function signInWithCredentials(page: Page, username: string, password: string) {
   36 |   // Navigate to sign-in page
   37 |   await page.goto('/auth/signin');
   38 |   await waitForPageLoad(page);
   39 |
   40 |   // Switch to credentials login if not already selected
   41 |   const credentialsTab = page.locator('button:has-text("Username/Password")');
   42 |   if (await credentialsTab.isVisible()) {
   43 |     await credentialsTab.click();
   44 |     await page.waitForTimeout(500); // Wait for tab switch animation
   45 |   }
   46 |
   47 |   // Fill in credentials
>  48 |   await page.fill('input[name="username"]', username);
      |              ^ Error: page.fill: Test timeout of 30000ms exceeded.
   49 |   await page.fill('input[name="password"]', password);
   50 |
   51 |   // Click sign in button
   52 |   await page.click('button[type="submit"]:has-text("Sign In")');
   53 |
   54 |   // Wait for redirect to dashboard
   55 |   await page.waitForURL('/dashboard', { timeout: 10000 });
   56 |   await waitForPageLoad(page);
   57 | }
   58 |
   59 | /**
   60 |  * Sign in with the test admin user (farbour/admin)
   61 |  */
   62 | export async function signInAsAdmin(page: Page) {
   63 |   await signInWithCredentials(page, 'farbour', 'admin');
   64 | }
   65 |
   66 | /**
   67 |  * Sign out from the application
   68 |  */
   69 | export async function signOut(page: Page) {
   70 |   // Look for user menu or sign out button
   71 |   const userMenu = page.locator('[data-testid="user-menu"]').or(
   72 |     page.locator('button:has-text("Sign out")')
   73 |   );
   74 |
   75 |   if (await userMenu.isVisible()) {
   76 |     await userMenu.click();
   77 |
   78 |     // Look for sign out option
   79 |     const signOutButton = page.locator('button:has-text("Sign out")').or(
   80 |       page.locator('[data-testid="sign-out"]')
   81 |     );
   82 |
   83 |     if (await signOutButton.isVisible()) {
   84 |       await signOutButton.click();
   85 |     }
   86 |   }
   87 |
   88 |   // Wait for redirect to sign-in page
   89 |   await page.waitForURL('/auth/signin');
   90 | }
   91 |
   92 | /**
   93 |  * Navigate to a specific page and wait for it to load
   94 |  */
   95 | export async function navigateToPage(page: Page, path: string) {
   96 |   await page.goto(path);
   97 |   await waitForPageLoad(page);
   98 | }
   99 |
  100 | /**
  101 |  * Check if user is authenticated
  102 |  */
  103 | export async function isAuthenticated(page: Page): Promise<boolean> {
  104 |   try {
  105 |     // Check if we're on the dashboard or any authenticated page
  106 |     const currentUrl = page.url();
  107 |     return !currentUrl.includes('/auth/signin');
  108 |   } catch {
  109 |     return false;
  110 |   }
  111 | }
  112 |
  113 | /**
  114 |  * Wait for a specific element to be visible
  115 |  */
  116 | export async function waitForElement(page: Page, selector: string, timeout = 10000) {
  117 |   await page.waitForSelector(selector, { state: 'visible', timeout });
  118 | }
  119 |
  120 | /**
  121 |  * Take a screenshot with a descriptive name
  122 |  */
  123 | export async function takeScreenshot(page: Page, name: string) {
  124 |   await page.screenshot({
  125 |     path: `tests/reports/e2e-artifacts/screenshots/${name}-${Date.now()}.png`,
  126 |     fullPage: true
  127 |   });
  128 | }
  129 |
  130 | /**
  131 |  * Check if the sidebar navigation is working
  132 |  */
  133 | export async function testSidebarNavigation(page: Page) {
  134 |   // Test main navigation items
  135 |   const navItems = [
  136 |     { text: 'Dashboard', url: '/dashboard' },
  137 |     { text: 'Brand Deep Dive', url: '/brand-deep-dive' },
  138 |     { text: 'Marketing Dashboard', url: '/marketing-dashboard' },
  139 |     { text: 'Executive Summary', url: '/executive-summary' },
  140 |     { text: 'Budget', url: '/budget' },
  141 |     { text: 'AI Assistant', url: '/ai-assistant' }
  142 |   ];
  143 |
  144 |   for (const item of navItems) {
  145 |     const navLink = page.locator(`nav a:has-text("${item.text}")`);
  146 |     if (await navLink.isVisible()) {
  147 |       await navLink.click();
  148 |       await page.waitForURL(`**${item.url}`);
```